{"name": "ai-companion-app-v2", "version": "0.0.1", "private": true, "scripts": {"build": "next build --turbopack", "dev": "next dev --turbopack", "format": "prettier --write .", "lint": "eslint .", "prepare": "husky", "start": "next start"}, "lint-staged": {"*.{js,jsx,ts,tsx,md,mdx,css,json}": ["prettier --write"], "*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.5.0", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.34.0", "@next/eslint-plugin-next": "^15.5.0", "@types/node": "^20.19.11", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.7", "eslint": "^9.34.0", "eslint-config-next": "15.5.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "prettier-plugin-packagejson": "^2.5.19", "prettier-plugin-sort-json": "^4.1.1", "prettier-plugin-tailwindcss": "^0.6.14", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2", "typescript-eslint": "^8.40.0"}, "packageManager": "pnpm@10.15.0"}